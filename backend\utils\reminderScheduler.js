const cron = require("node-cron");
const Client = require("../models/Client");
const { sendPaymentReminder } = require("./emailService");

// Function to send 3-day reminders
const send3DayReminders = async () => {
  try {
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);

    const clients = await Client.find({
      policyStatus: "active",
      nextDueDate: {
        $gte: new Date(threeDaysFromNow.setHours(0, 0, 0, 0)),
        $lt: new Date(threeDaysFromNow.setHours(23, 59, 59, 999)),
      },
      "remindersSent.type": { $ne: "3_day" },
    });

    for (const client of clients) {
      const emailSent = await sendPaymentReminder(client, "3_day");
      if (emailSent) {
        client.remindersSent.push({
          type: "3_day",
          sentDate: new Date(),
          dueDate: client.nextDueDate,
        });
        await client.save();
      }
    }

    console.log(`3-day reminders processed: ${clients.length} clients`);
  } catch (error) {
    console.error("Error sending 3-day reminders:", error);
  }
};

// Function to send 6-hour reminders
const send6HourReminders = async () => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const clients = await Client.find({
      policyStatus: "active",
      nextDueDate: {
        $gte: today,
        $lt: tomorrow,
      },
      isPaymentMadeThisMonth: false,
      "remindersSent.type": { $ne: "6_hour" },
    });

    for (const client of clients) {
      const emailSent = await sendPaymentReminder(client, "6_hour");
      if (emailSent) {
        client.remindersSent.push({
          type: "6_hour",
          sentDate: new Date(),
          dueDate: client.nextDueDate,
        });
        await client.save();
      }
    }

    console.log(`6-hour reminders processed: ${clients.length} clients`);
  } catch (error) {
    console.error("Error sending 6-hour reminders:", error);
  }
};

// Function to reset monthly payment status
const resetMonthlyPaymentStatus = async () => {
  try {
    await Client.updateMany(
      { isPaymentMadeThisMonth: true },
      {
        $set: { isPaymentMadeThisMonth: false },
        $unset: { remindersSent: 1 },
      }
    );
    console.log("Monthly payment status reset completed");
  } catch (error) {
    console.error("Error resetting monthly payment status:", error);
  }
};

const start = () => {
  // Run 3-day reminders daily at 9:00 AM
  cron.schedule("0 9 * * *", send3DayReminders);

  // Run 6-hour reminders every 6 hours
  cron.schedule("0 */6 * * *", send6HourReminders);

  // Reset monthly payment status on the 1st of each month at midnight
  cron.schedule("0 0 1 * *", resetMonthlyPaymentStatus);

  console.log("Payment reminder scheduler started");
};

module.exports = {
  start,
  send3DayReminders,
  send6HourReminders,
  resetMonthlyPaymentStatus,
};
