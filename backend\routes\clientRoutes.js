const express = require("express");
const { body } = require("express-validator");
const {
  getClients,
  getClient,
  createClient,
  updateClient,
  deleteClient,
  getDashboardStats,
} = require("../controllers/clientController");

const router = express.Router();

// Validation rules
const clientValidation = [
  body("policyNumber").notEmpty().withMessage("Policy number is required"),
  body("firstName").notEmpty().withMessage("First name is required"),
  body("lastName").notEmpty().withMessage("Last name is required"),
  body("email").isEmail().withMessage("Valid email is required"),
  body("phoneNumber").notEmpty().withMessage("Phone number is required"),
  body("paymentDueDate")
    .isInt({ min: 1, max: 31 })
    .withMessage("Payment due date must be between 1 and 31"),
  body("amount")
    .isFloat({ min: 0 })
    .withMessage("Amount must be a positive number"),
  body("frequency")
    .isIn(["monthly", "quarterly", "annually"])
    .withMessage("Invalid frequency"),
  body("paymentMethod")
    .isIn(["bank_transfer", "credit_card", "debit_card", "check"])
    .withMessage("Invalid payment method"),
  body("policyStartDate")
    .isISO8601()
    .withMessage("Valid policy start date is required"),
];

router.get("/dashboard", getDashboardStats);
router.get("/", getClients);
router.get("/:id", getClient);
router.post("/", clientValidation, createClient);
router.put("/:id", clientValidation, updateClient);
router.delete("/:id", deleteClient);

module.exports = router;
