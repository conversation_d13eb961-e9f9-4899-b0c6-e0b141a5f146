const { createTransporter } = require("../config/email");

const sendPaymentReminder = async (client, reminderType) => {
  const transporter = createTransporter();

  let subject, htmlContent;

  if (reminderType === "3_day") {
    subject = "Payment Due Reminder - Kaphy Insurance";
    htmlContent = `
      
        Payment Reminder - 3 Days Notice
        Dear ${client.firstName} ${client.lastName},
        This is a friendly reminder that your insurance payment is due in 3 days.
        
          Payment Details:
          Policy Number: ${client.policyNumber}
          Amount Due: $${client.amount.toFixed(2)}
          Due Date: ${client.nextDueDate.toDateString()}
          Payment Method: ${client.paymentMethod
            .replace("_", " ")
            .toUpperCase()}
        
        Please ensure your payment is processed on time to avoid any lapses in coverage.
        If you have any questions, please contact <NAME_EMAIL>
        Best regards,Kaphy Insurance Agency
      
    `;
  } else if (reminderType === "6_hour") {
    subject = "Payment Due Today - Final Reminder";
    htmlContent = `
      
        Final Payment Reminder - Due Today
        Dear ${client.firstName} ${client.lastName},
        URGENT: Your insurance payment is due today and will be automatically withdrawn in 6 hours.
        
          Payment Details:
          Policy Number: ${client.policyNumber}
          Amount Due: $${client.amount.toFixed(2)}
          Due Date: ${client.nextDueDate.toDateString()}
          Payment Method: ${client.paymentMethod
            .replace("_", " ")
            .toUpperCase()}
        
        Please ensure sufficient funds are available in your account to avoid any payment failures.
        If you have any concerns, please contact us <NAME_EMAIL>
        Best regards,Kaphy Insurance Agency
      
    `;
  }

  const mailOptions = {
    from: `"Kaphy Insurance Agency" <${process.env.EMAIL_USER}>`,
    to: client.email,
    subject: subject,
    html: htmlContent,
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log(`${reminderType} reminder sent to ${client.email}`);
    return true;
  } catch (error) {
    console.error(`Failed to send email to ${client.email}:`, error);
    return false;
  }
};

module.exports = {
  sendPaymentReminder,
};
