const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");
const connectDB = require("./config/database");
const clientRoutes = require("./routes/clientRoutes");
const paymentRoutes = require("./routes/paymentRoutes");
const errorHandler = require("./middleware/errorHandler");
const reminderScheduler = require("./utils/reminderScheduler");

dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Connect to MongoDB
connectDB();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use("/api/clients", clientRoutes);
app.use("/api/payments", paymentRoutes);

// Error handling middleware
app.use(errorHandler);

// Start reminder scheduler
reminderScheduler.start();

// Health check route
app.get("/api/health", (req, res) => {
  res.json({ status: "OK", message: "Kaphy Insurance API is running" });
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
