const PaymentHistory = require("../models/PaymentHistory");
const Client = require("../models/Client");

// Record payment
const recordPayment = async (req, res) => {
  try {
    const { clientId, amount, paymentMethod, transactionId, notes } = req.body;

    const client = await Client.findById(clientId);
    if (!client) {
      return res.status(404).json({ message: "Client not found" });
    }

    const payment = new PaymentHistory({
      clientId,
      policyNumber: client.policyNumber,
      amount,
      paymentDate: new Date(),
      paymentMethod,
      transactionId,
      notes,
    });

    await payment.save();

    // Update client payment status
    client.lastPaymentDate = new Date();
    client.isPaymentMadeThisMonth = true;
    client.nextDueDate = client.calculateNextDueDate();
    await client.save();

    res.status(201).json(payment);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get payment history for a client
const getPaymentHistory = async (req, res) => {
  try {
    const { clientId } = req.params;
    const { page = 1, limit = 10 } = req.query;

    const payments = await PaymentHistory.find({ clientId })
      .sort({ paymentDate: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await PaymentHistory.countDocuments({ clientId });

    res.json({
      payments,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get all payments
const getAllPayments = async (req, res) => {
  try {
    const { page = 1, limit = 10, startDate, endDate } = req.query;
    const query = {};

    if (startDate && endDate) {
      query.paymentDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    }

    const payments = await PaymentHistory.find(query)
      .populate("clientId", "firstName lastName policyNumber")
      .sort({ paymentDate: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await PaymentHistory.countDocuments(query);

    res.json({
      payments,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

module.exports = {
  recordPayment,
  getPaymentHistory,
  getAllPayments,
};
