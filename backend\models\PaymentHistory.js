const mongoose = require("mongoose");

const paymentHistorySchema = new mongoose.Schema(
  {
    clientId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Client",
      required: true,
    },
    policyNumber: {
      type: String,
      required: true,
    },
    amount: {
      type: Number,
      required: true,
    },
    paymentDate: {
      type: Date,
      required: true,
    },
    paymentMethod: {
      type: String,
      enum: ["bank_transfer", "credit_card", "debit_card", "check"],
      required: true,
    },
    status: {
      type: String,
      enum: ["successful", "failed", "pending"],
      default: "successful",
    },
    transactionId: {
      type: String,
      trim: true,
    },
    notes: {
      type: String,
      trim: true,
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model("PaymentHistory", paymentHistorySchema);
