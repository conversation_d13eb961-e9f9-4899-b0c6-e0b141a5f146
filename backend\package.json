{"name": "kaphy-insurance-backend", "version": "1.0.0", "description": "Payment reminder system backend for Kaphy Insurance Agency", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js"}, "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.1", "node-cron": "^4.2.1", "nodemailer": "^7.0.5"}, "devDependencies": {"nodemon": "^3.1.10"}}