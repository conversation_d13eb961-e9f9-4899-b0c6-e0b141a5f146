const mongoose = require("mongoose");

const clientSchema = new mongoose.Schema(
  {
    policyNumber: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    firstName: {
      type: String,
      required: true,
      trim: true,
    },
    lastName: {
      type: String,
      required: true,
      trim: true,
    },
    email: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
    },
    phoneNumber: {
      type: String,
      required: true,
      trim: true,
    },
    paymentDueDate: {
      type: Number, // Day of month (1-31)
      required: true,
      min: 1,
      max: 31,
    },
    amount: {
      type: Number,
      required: true,
      min: 0,
    },
    frequency: {
      type: String,
      enum: ["monthly", "quarterly", "annually"],
      required: true,
      default: "monthly",
    },
    nextDueDate: {
      type: Date,
      required: true,
    },
    paymentMethod: {
      type: String,
      enum: ["bank_transfer", "credit_card", "debit_card", "check"],
      required: true,
    },
    lastPaymentDate: {
      type: Date,
    },
    policyStatus: {
      type: String,
      enum: ["active", "inactive", "lapsed"],
      default: "active",
    },
    isPaymentMadeThisMonth: {
      type: Boolean,
      default: false,
    },
    preferredNotificationMethod: {
      type: String,
      enum: ["email", "sms", "both"],
      default: "email",
    },
    policyStartDate: {
      type: Date,
      required: true,
    },
    notes: {
      type: String,
      trim: true,
    },
    remindersSent: [
      {
        type: {
          type: String,
          enum: ["3_day", "6_hour"],
        },
        sentDate: Date,
        dueDate: Date,
      },
    ],
  },
  {
    timestamps: true,
  }
);

// Index for efficient queries
clientSchema.index({ nextDueDate: 1, policyStatus: 1 });
clientSchema.index({ policyNumber: 1 });
clientSchema.index({ email: 1 });

// Method to calculate next due date
clientSchema.methods.calculateNextDueDate = function () {
  const today = new Date();
  const currentMonth = today.getMonth();
  const currentYear = today.getFullYear();

  let nextDue = new Date(currentYear, currentMonth, this.paymentDueDate);

  // If the due date has passed this month, move to next month
  if (nextDue <= today) {
    if (this.frequency === "monthly") {
      nextDue.setMonth(nextDue.getMonth() + 1);
    } else if (this.frequency === "quarterly") {
      nextDue.setMonth(nextDue.getMonth() + 3);
    } else if (this.frequency === "annually") {
      nextDue.setFullYear(nextDue.getFullYear() + 1);
    }
  }

  return nextDue;
};

module.exports = mongoose.model("Client", clientSchema);
